import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  DatePicker,
  DEFAULT_LIMIT,
  EHTMLType,
  FileInput,
  FormAutocomplete,
  FormCheck,
  FormInput,
  FormLabel,
  FormSelect,
  Row,
  Tooltip,
  useToast,
  useTranslate,
} from "@buymed/solidjs-component/components";
import {
  API_STATUS,
  formatCurrency,
  formatNumber,
  isEmptyObject,
} from "@buymed/solidjs-component/utils";
import { Index, Show, createSignal, createResource } from "solid-js";
import { useParams } from "solid-start";
import { BID_STATUS } from "~/services/tender/bid.model";
import {
  CONTRACT_STATUS,
  CONTRACT_STATUS_OPTIONS,
  CONTRACT_TYPE_OPTIONS,
} from "~/services/tender/contract.model";
import { LEGAL_ENTITY_BANK_OPTIONS, LEGAL_ENTITY_OPTIONS } from "~/services/tender/legal.model";
import SaveIcon from "~icons/mdi/content-save";
import Mdi<PERSON><PERSON><PERSON>ox from "~icons/mdi/minus-box";
import MdiPlusBox from "~icons/mdi/plus-box";
import { BidSelectAutocomplete } from "../BidSelectAutocomplete";
import { ContractAnnexTable } from "../ContractAnnexTable";
import { LotSelectAutoComplete } from "../LotSelectAutoComplete";
import MdiPlay from "~icons/mdi/play";
import { updateContract } from "~/services/tender/contract.client";
import { useAuth } from "~/contexts/AuthContext";
import { AutoCompleteTender } from "~/components/Autocomplete";
import { getBeneficiaryList } from "~/services/tender/beneficiary.client";

export function ContractFormCreate(props) {
  const { t } = useTranslate();
  const params = useParams();
  const toast = useToast();
  const { documentToken } = useAuth();

  // Signal to track if we need to auto-populate beneficiary data
  const [] = createSignal(null);

  async function handleActiveContract() {
    if (!props.hookForm.data("signingDate")) {
      props.hookForm.setErrors("signingDate", "Vui lòng cập nhật thời gian ký hợp đồng");
      toast.error(
        t("common:notify.action_fail", { error: "Vui lòng cập nhật thời gian ký hợp đồng" })
      );
      return;
    }

    if (!props.hookForm.data("expireDate")) {
      props.hookForm.setErrors("expireDate", "Vui lòng cập nhật thời gian hết hạn hợp đồng");
      toast.error(
        t("common:notify.action_fail", {
          error: "Vui lòng cập nhật thời gian hết hạn hợp đồng",
        })
      );
      return;
    }

    const res = await updateContract({ code: params.code, status: CONTRACT_STATUS.ACTIVE });
    if (res.status !== API_STATUS.OK) {
      console.error("[Error] Update contract:", res);
      toast.error(t("common:notify.action_fail", { error: res.message }));
    } else {
      toast.success(t`common:notify.update_success`);
      location.reload(); // reload data with all select box
    }
    return;
  }

  function getAmount(lotPrice, quantity) {
    const price = lotPrice || 0;
    const qty = quantity || 0;
    return price * qty;
  }

  const handleFetchData = async ({ search }) => {
    if (!search) {
      return [];
    }

    const res = await getBeneficiaryList({
      search: search,
      limit: DEFAULT_LIMIT,
      offset: 0,
    });
    if (res.status !== API_STATUS.OK) {
      console.error("[Error] fetch beneficiaryOptions", res);
      return [];
    }
    return res.data;
  };

  return (
    <Row class="gap-3">
      <form ref={props.hookForm.form}>
        <Card>
          <CardBody>
            <section class="d-flex flex-column row-gap-3">
              <header class="section-header">Thông Tin Chủ Đầu Tư</header>
              <Row class="row-gap-3">
                <Col xs={12} md={6} lg={4}>
                  <BidSelectAutocomplete
                    name="bidID"
                    status={BID_STATUS.WIN}
                    placeholder={t`contract:placeholder.under_bid`}
                    label={t`contract:label.under_bid`}
                    disabled={props.contract}
                    invalid={!isEmptyObject(props.hookForm.errors("bidID"))}
                    feedbackInvalid={props.hookForm.errors("bidID")}
                    onChange={(e) => {
                      if (!props.contract) {
                        props.hookForm.setFields(`contractNumber`, e.data.contractNo);
                      }
                      props.hookForm.setFields(`procuringEntity`, e.data.procuringEntity);
                    }}
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="procuringEntity"
                    id="procuringEntity"
                    label="Đơn vị mời thầu"
                    disabled
                  />
                </Col>
              </Row>
              <Row class="row-gap-3">
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="contractNumber"
                    id="contractNumber"
                    label="Mã hợp đồng"
                    // disabled={props.contract}
                    placeholder="Nhập mã hợp đồng"
                    invalid={!isEmptyObject(props.hookForm.errors("contractNumber"))}
                    feedbackInvalid={props.hookForm.errors("contractNumber")}
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={8}>
                  <FormInput
                    name="name"
                    id="name"
                    label="Tên hợp đồng"
                    placeholder="Nhập tên hợp đồng"
                    invalid={!isEmptyObject(props.hookForm.errors("name"))}
                    feedbackInvalid={props.hookForm.errors("name")}
                    required
                  />
                </Col>
              </Row>
              <Row class="row-gap-3">
                <Col xs={12} md={6} lg={4}>
                  {/* <FormInput
                                        name="beneficiaryName"
                                        id="beneficiaryName"
                                        label="Đơn vị thụ hưởng"
                                        placeholder="Nhập tên đơn vị/tổ chức"
                                        invalid={!isEmptyObject(props.hookForm.errors("beneficiaryName"))}
                                        feedbackInvalid={props.hookForm.errors("beneficiaryName")}
                                        required
                                    /> */}

                  <AutoCompleteTender
                    name="beneficiaryName"
                    label="Đơn vị thụ hưởng"
                    placeholder="Nhập tên đơn vị/tổ chức"
                    handleFetchData={handleFetchData}
                    fieldKey="code"
                    fieldValue="name"
                    renderOption={(props, { data }) => (
                      <li {...props}>
                        <b>
                          {data.beneficiaryID}&nbsp;-&nbsp;{data.name}
                        </b>
                      </li>
                    )}
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="phoneNumber"
                    id="phoneNumber"
                    label="Số điện thoại"
                    invalid={!isEmptyObject(props.hookForm.errors("phoneNumber"))}
                    feedbackInvalid={props.hookForm.errors("phoneNumber")}
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="taxCode"
                    id="taxCode"
                    label="Mã số thuế"
                    invalid={!isEmptyObject(props.hookForm.errors("taxCode"))}
                    feedbackInvalid={props.hookForm.errors("taxCode")}
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="address"
                    id="address"
                    label="Địa chỉ"
                    invalid={!isEmptyObject(props.hookForm.errors("address"))}
                    feedbackInvalid={props.hookForm.errors("address")}
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="email"
                    id="email"
                    label="Email"
                    invalid={!isEmptyObject(props.hookForm.errors("email"))}
                    feedbackInvalid={props.hookForm.errors("email")}
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="budgetUnitCode"
                    id="budgetUnitCode"
                    label="Mã số của đơn vị có quan hệ với ngân sách"
                    // invalid={!isEmptyObject(props.hookForm.errors("BudgetUnitCode"))}
                    // feedbackInvalid={props.hookForm.errors("BudgetUnitCode")}
                  />
                </Col>
              </Row>
            </section>
          </CardBody>
        </Card>
        <br />
        <Card>
          <CardBody>
            <section class="d-flex flex-column row-gap-3">
              <header class="section-header">Thông Tin Nhà thầu</header>
            </section>
            <Row class="row-gap-3">
              <Col xs={12} md={6} lg={4}>
                <FormAutocomplete
                  name="legalEntityCode"
                  id="legalEntityCode"
                  label="Chọn pháp nhân"
                  options={LEGAL_ENTITY_OPTIONS}
                  invalid={!isEmptyObject(props.hookForm.errors("legalEntityCode"))}
                  feedbackInvalid={props.hookForm.errors("legalEntityCode")}
                  onChange={(e) => {
                    props.hookForm.setFields("legalEntityName", e.data.name);
                    props.hookForm.setFields("legalEntityTaxCode", e.data.taxCode);
                    props.hookForm.setFields("legalEntityAddress", e.data.address);
                    props.hookForm.setFields("legalEntityEmail", e.data.email);
                    props.hookForm.setFields("legalEntityTel", e.data.tel);
                  }}
                  disabled={props.contract && props.contract?.legalEntityCode === "TAYAU"}
                  required
                />
              </Col>
              <Show when={props.hookForm.data(`legalEntityCode`)}>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="legalEntityName"
                    id="legalEntityName"
                    label="Tên công ty"
                    disabled
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="legalEntityTaxCode"
                    id="legalEntityTaxCode"
                    label="Mã số thuế"
                    disabled
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="legalEntityAddress"
                    id="legalEntityAddress"
                    label="Địa chỉ"
                    disabled
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="legalEntityEmail"
                    id="legalEntityEmail"
                    label="Email"
                    disabled
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="legalEntityTel"
                    id="legalEntityTel"
                    label="Số điện thoại"
                    disabled
                    required
                  />
                </Col>
                <Col xs={12} md={12} lg={8}>
                  <FormSelect
                    name="legalEntityBankCode"
                    id="legalEntityBankCode"
                    label="Chọn ngân hàng"
                    // disabled={props.contract}
                    options={LEGAL_ENTITY_BANK_OPTIONS}
                    invalid={!isEmptyObject(props.hookForm.errors("legalEntityBankCode"))}
                    feedbackInvalid={props.hookForm.errors("legalEntityBankCode")}
                    required
                  />
                </Col>
              </Show>
            </Row>
          </CardBody>
        </Card>
        <br />
        <Card>
          <CardBody>
            <section class="d-flex flex-column row-gap-3">
              <header class="section-header">Nội dung</header>
              <Row class="row-gap-3">
                <Col xs={12} md={6} lg={4}>
                  <FormSelect
                    name="contractType"
                    id="contractType"
                    label="Loại hợp đồng"
                    options={CONTRACT_TYPE_OPTIONS(t)}
                    invalid={!isEmptyObject(props.hookForm.errors("contractType"))}
                    feedbackInvalid={props.hookForm.errors("contractType")}
                    disabled
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="debtLimit"
                    id="debtLimit"
                    type="number"
                    label="Giới hạn công nợ"
                    text={
                      props.hookForm.data("debtLimit")
                        ? formatCurrency(props.hookForm.data("debtLimit"))
                        : ""
                    }
                    invalid={!isEmptyObject(props.hookForm.errors("debtLimit"))}
                    feedbackInvalid={props.hookForm.errors("debtLimit")}
                    required
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <FormInput
                    name="paymentTerm"
                    id="paymentTerm"
                    type="number"
                    label="Kì hạn thanh toán"
                    endAdornment={"Ngày"}
                    invalid={!isEmptyObject(props.hookForm.errors("paymentTerm"))}
                    feedbackInvalid={props.hookForm.errors("paymentTerm")}
                    required
                  />
                </Col>
              </Row>
              <Row class="row-gap-3">
                <Col xs={12} md={6} lg={4}>
                  <DatePicker
                    label={t`contract:signing_date`}
                    name="signingDate"
                    id="signingDate"
                    format="dd/MM/yyyy"
                    placeholder="Chọn"
                    invalid={!isEmptyObject(props.hookForm.errors("signingDate"))}
                    feedbackInvalid={props.hookForm.errors("signingDate")}
                    required={
                      props.hookForm.data("status") &&
                      props.hookForm.data("status") != CONTRACT_STATUS.DRAFT
                    }
                  />
                </Col>
                <Col xs={12} md={6} lg={4}>
                  <DatePicker
                    label={t`contract:expire_date`}
                    name="expireDate"
                    id="expireDate"
                    placeholder="Chọn"
                    format="dd/MM/yyyy"
                    invalid={!isEmptyObject(props.hookForm.errors("expireDate"))}
                    feedbackInvalid={props.hookForm.errors("expireDate")}
                  />
                </Col>
              </Row>
            </section>
          </CardBody>
        </Card>
        <br />
        <Card>
          <CardBody>
            <section class="d-flex flex-column row-gap-3">
              <header class="section-header">Tài liệu đính kèm</header>
              <Row class="row-gap-3">
                <Col xs={12}>
                  <Index each={props.hookForm.data("attachments")}>
                    {(attachment, index) => (
                      <FileInput
                        documentToken={documentToken()}
                        mode={EHTMLType.Documents}
                        type={EHTMLType.Documents}
                        extensions=".xlsx, .xls, .doc, .docx, .ppt, .pptx, .pdf"
                        onAdd={(newFile) => {
                          console.log("newFile=", newFile);
                          props.hookForm.setData("attachments", [
                            newFile.previewLink,
                            ...props.hookForm.data("attachments"),
                          ]);
                        }}
                        onRemove={() =>
                          props.hookForm.setData(
                            "attachments",
                            props.hookForm.data("attachments")?.filter((_, i) => i !== index)
                          )
                        }
                        value={attachment}
                      />
                    )}
                  </Index>
                </Col>
                <Col xs={12}>
                  <FormLabel>(Bao gồm: hợp đồng)</FormLabel>
                </Col>
              </Row>
            </section>
          </CardBody>
        </Card>
        <br />
        <Card>
          <CardBody>
            <section class="d-flex flex-column row-gap-3">
              <header class="section-header">Tài liệu đính kèm</header>
              <Row class="row-gap-3">
                <Col xs={12}>
                  <Index each={props.hookForm.data("extendAttachments")}>
                    {(attachment, index) => (
                      <FileInput
                        documentToken={documentToken()}
                        mode={EHTMLType.Documents}
                        type={EHTMLType.Documents}
                        extensions=".xlsx, .xls, .doc, .docx, .ppt, .pptx, .pdf"
                        onAdd={(newFile) => {
                          console.log("newFile=", newFile);
                          props.hookForm.setData("extendAttachments", [
                            newFile.previewLink,
                            ...props.hookForm.data("extendAttachments"),
                          ]);
                        }}
                        onRemove={() =>
                          props.hookForm.setData(
                            "extendAttachments",
                            props.hookForm.data("extendAttachments")?.filter((_, i) => i !== index)
                          )
                        }
                        value={attachment}
                      />
                    )}
                  </Index>
                </Col>
                <Col xs={12}>
                  <FormLabel>(Bao gồm: phụ lục theo hợp đồng chính)</FormLabel>
                </Col>
              </Row>
            </section>
          </CardBody>
        </Card>
        <br />
        <Show when={props.contract}>
          <br />
          <Card>
            <CardBody>
              <section class="d-flex flex-column row-gap-3">
                <header class="section-header">Trạng thái hợp đồng</header>
                <Row class="row-gap-3">
                  <Col xs={12} md={6} lg={4}>
                    <FormSelect
                      name="status"
                      id="status"
                      label={t`contract:contract_status`}
                      options={CONTRACT_STATUS_OPTIONS(t)}
                      disabled={props.contract.status == CONTRACT_STATUS.DRAFT}
                      required
                    />
                  </Col>
                  <Col xs={12} md={6} lg={4}>
                    <FormLabel>&nbsp</FormLabel>
                    <FormCheck
                      color="primary"
                      name="isStoreContractDocument"
                      id="isStoreContractDocument"
                      label="Đã nhận bản lưu hợp đồng"
                    />
                  </Col>
                </Row>
              </section>
            </CardBody>
          </Card>
          <br />
        </Show>

        <Show when={props.contract}>
          <br />
          <Card>
            <CardBody>
              <section class="d-flex flex-column row-gap-3">
                <div class="d-flex align-items-center justify-content-between">
                  <header class="section-header">Phụ lục hợp đồng</header>
                  <Show when={true}>
                    <Tooltip content="Thêm phụ lục">
                      <Button
                        color="second"
                        href={`/contract/${params.code}/annex/new`}
                        startIcon={<MdiPlusBox class="fs-4" />}
                      ></Button>
                    </Tooltip>
                  </Show>
                </div>

                <Row class="row-gap-3">
                  <ContractAnnexTable annexes={props.annexes} total={0} bidMap={null} />
                </Row>
              </section>
            </CardBody>
          </Card>
          <br />
        </Show>

        <Show when={props.hookForm.data("bidID")}>
          <Index each={props.hookForm.data("products")}>
            {(_product, index) => (
              <>
                <Card>
                  <CardBody>
                    <section class="d-flex flex-column row-gap-3">
                      <div class="d-flex align-items-center justify-content-between">
                        <header class="section-header">Sản phẩm thứ {index + 1}</header>
                        <Show when={index + 1 == props.hookForm.data("products").length}>
                          <Tooltip content="Thêm sản phẩm">
                            <Button
                              color="second"
                              onClick={props.hookForm.addField(
                                `products`,
                                {},
                                props.hookForm.data("products").length
                              )}
                              startIcon={<MdiPlusBox class="fs-4" />}
                            ></Button>
                          </Tooltip>
                        </Show>
                        <Show when={index + 1 != props.hookForm.data("products").length}>
                          <Tooltip content="Xoá sản phẩm">
                            <Button
                              color="second"
                              onClick={() => props.hookForm.unsetField(`products.${index}`)}
                              startIcon={<MdiMinusBox class="fs-4" />}
                            ></Button>
                          </Tooltip>
                        </Show>
                      </div>
                      <Row class="row-gap-3">
                        <Col xs={12} md={6} lg={6}>
                          <LotSelectAutoComplete
                            name={`products.${index}.lotID`}
                            id={`products.${index}.lotID`}
                            placeholder="Chọn lô sản phẩm"
                            label="Lô sản phẩm"
                            bidID={props.hookForm.data("bidID")}
                            onChange={(e) => {
                              props.hookForm.setFields(`products.${index}.price`, e.data.lotPrice);
                              props.hookForm.setFields(`products.${index}.productID`, e.productID);
                              props.hookForm.setFields(
                                `products.${index}.productCode`,
                                e.data.productCode
                              );
                              props.hookForm.setFields(`products.${index}.lotName`, e.name);
                              props.hookForm.setFields(`products.${index}.unit`, e.data.unit);
                            }}
                            invalid={
                              !isEmptyObject(props.hookForm.errors(`products.${index}.lotID`))
                            }
                            feedbackInvalid={props.hookForm.errors(`products.${index}.lotID`)}
                          />
                        </Col>
                        <Col xs={12} md={6} lg={3}>
                          <FormInput
                            name={`products.${index}.price`}
                            id={`products.${index}.price`}
                            label="Giá trúng thầu"
                            type="number"
                            text={
                              props.hookForm.data(`products.${index}.price`)
                                ? formatCurrency(props.hookForm.data(`products.${index}.price`)) +
                                  "/" +
                                  props.hookForm.data(`products.${index}.unit`)
                                : ""
                            }
                            disabled
                          />
                        </Col>
                        <Col xs={12} md={6} lg={3}>
                          <FormInput
                            name={`products.${index}.quantity`}
                            id={`products.${index}.quantity`}
                            label="SL trúng thầu"
                            type="number"
                            placeholder="Nhập số lượng"
                            text={
                              props.hookForm.data(`products.${index}.quantity`)
                                ? formatNumber(props.hookForm.data(`products.${index}.quantity`)) +
                                  ` ` +
                                  props.hookForm.data(`products.${index}.unit`) +
                                  ` -> ` +
                                  formatCurrency(
                                    getAmount(
                                      props.hookForm.data(`products.${index}.price`),
                                      props.hookForm.data(`products.${index}.quantity`)
                                    )
                                  )
                                : ""
                            }
                            required
                          />
                        </Col>
                      </Row>
                    </section>
                  </CardBody>
                </Card>
                <br />
              </>
            )}
          </Index>
        </Show>
        <br />
        <Show when={true}>
          <div class="submit-wrapper">
            <Show when={props.contract && props.contract.status == CONTRACT_STATUS.DRAFT}>
              <Tooltip content={`Chuyển hợp đồng sang trạng thái đang thực hiện`}>
                <Button
                  color="success"
                  disabled={
                    !props.hookForm.data("signingDate") || !props.hookForm.data("expireDate")
                  }
                  class="ms-2"
                  onClick={handleActiveContract}
                >
                  <MdiPlay class="fs-5" />
                  Submit
                </Button>
              </Tooltip>
            </Show>
            <Tooltip content={t`common:button.save`}>
              <Button color="success" class="ms-2" type="submit">
                <SaveIcon class="fs-5" />
                Lưu thông tin
              </Button>
            </Tooltip>
          </div>
        </Show>
      </form>
    </Row>
  );
}
