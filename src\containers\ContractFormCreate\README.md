# ContractFormCreate - AutoCompleteTender Pre-selection Fix

## Overview

The `ContractFormCreate` component has been enhanced to properly display pre-selected beneficiary data when editing existing contracts.

## Problem Solved

**Issue**: AutoCompleteTender component was not showing the previously selected beneficiary when editing a contract, even though the data was available in `props.contract?.beneficiaryName`.

**Root Cause**: The `AutoCompleteTender` component requires both `defaultValue` AND `initialOptions` props to properly display a pre-selected value. The `defaultValue` alone only sets the search input value, not the selected option.

## Solution Implementation

### 1. **Enhanced handleFetchData Function**

The `handleFetchData` function now accepts both `search` and `beneficiaryName` parameters:

```javascript
const handleFetchData = async ({ search, beneficiaryName }) => {
  // If no search term and no beneficiaryName, return empty
  if (!search && !beneficiaryName) {
    return [];
  }

  const res = await getBeneficiaryList({
    search: search || beneficiaryName,
    limit: DEFAULT_LIMIT,
    offset: 0,
  });
  if (res.status !== API_STATUS.OK) {
    console.error("[Error] fetch beneficiaryOptions", res);
    return [];
  }
  return res.data;
};
```

### 2. **Beneficiary Data Resource**

Created a `createResource` to automatically fetch and format beneficiary data for edit mode:

```javascript
const [beneficiaryData] = createResource(
  () => props.contract?.beneficiaryName,
  async (beneficiaryName) => {
    if (!beneficiaryName) return null;

    const data = await handleFetchData({ search: beneficiaryName, beneficiaryName });
    if (data && data.length > 0) {
      const beneficiary = data[0];
      return {
        value: beneficiary.code,
        label: beneficiary.name,
        data: {
          id: beneficiary.code,
          beneficiaryID: beneficiary.beneficiaryID,
          name: beneficiary.name,
          ...beneficiary,
        },
      };
    }
    return null;
  }
);
```

### 3. **Updated AutoCompleteTender Component**

Added the `initialOptions` prop to properly display the pre-selected value:

```jsx
<AutoCompleteTender
  name="beneficiaryName"
  label="Đơn vị thụ hưởng"
  placeholder="Nhập tên đơn vị/tổ chức"
  handleFetchData={handleFetchData}
  defaultValue={props.contract?.beneficiaryName}
  initialOptions={beneficiaryData() ? [beneficiaryData()] : []}
  fieldKey="code"
  fieldValue="name"
  renderOption={(props, { data }) => (
    <li {...props}>
      <b>
        {data.beneficiaryID}&nbsp;-&nbsp;{data.name}
      </b>
    </li>
  )}
/>
```

## How It Works

### Edit Mode Flow:

1. **Component receives contract data** with `beneficiaryName`
2. **createResource automatically triggers** when `props.contract?.beneficiaryName` exists
3. **API call fetches beneficiary details** using the beneficiary name
4. **Data is formatted** into the correct option structure for AutoCompleteTender
5. **initialOptions prop** receives the formatted beneficiary data
6. **AutoCompleteTender displays** the pre-selected beneficiary

### Create Mode Flow:

1. **No contract prop** means create mode
2. **beneficiaryData resource returns null**
3. **initialOptions is empty array**
4. **User manually searches** and selects beneficiaries

## Key Benefits

✅ **Automatic pre-selection**: Edit mode shows existing beneficiary without user action
✅ **Seamless UX**: No flickering or empty states when loading edit forms
✅ **Backward compatible**: Create mode works exactly as before
✅ **Type safe**: Proper data structure matching AutoCompleteTender expectations
✅ **Performance optimized**: Only fetches data when needed in edit mode

## Technical Details

### AutoCompleteTender Component Requirements:

- `defaultValue`: Sets the search input text
- `initialOptions`: Sets the pre-selected option(s)
- `fieldKey`: Maps to the value field ("code")
- `fieldValue`: Maps to the display field ("name")

### Data Structure:

```javascript
{
  value: "beneficiary_code",      // Used for form submission
  label: "Beneficiary Name",      // Displayed in dropdown
  data: {                         // Additional data for renderOption
    id: "beneficiary_code",
    beneficiaryID: "12345",
    name: "Beneficiary Name",
    // ... other beneficiary fields
  }
}
```

## Usage Examples

### Edit Mode (Automatic):

```jsx
<ContractFormCreate
  hookForm={hookForm}
  annexes={annexList}
  contract={contractInfo} // Triggers auto-population
/>
```

### Create Mode (Manual):

```jsx
<ContractFormCreate
  hookForm={hookForm}
  // No contract prop - manual selection
/>
```
